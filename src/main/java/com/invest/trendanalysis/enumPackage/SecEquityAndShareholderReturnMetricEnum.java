package com.invest.trendanalysis.enumPackage;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor()
public enum SecEquityAndShareholderReturnMetricEnum {
    COMMON_STOCK_SHARES_OUTSTANDING("CommonStockSharesOutstanding","流通在外的普通股股数"),
    WEIGHTED_AVERAGE_SHARES_BASIC("WeightedAverageSharesBasic","基本加权平均流通股数"),
    WEIGHTED_AVERAGE_SHARES_DILUTED("WeightedAverageSharesDiluted","稀释后加权平均流通股数"),
    REPURCHASE_AUTHORIZED_AMOUNT("RepurchaseAuthorizedAmount","授权回购总额"),
    REPURCHASE_REMAINING_AMOUNT("RepurchaseRemainingAmount","剩余可回购额度"),
    COMMON_STOCK_SHARES_AUTHORIZED("CommonStockSharesAuthorized","授权发行的普通股总数");
    private final String metric;
    private final String metricDescription;
}