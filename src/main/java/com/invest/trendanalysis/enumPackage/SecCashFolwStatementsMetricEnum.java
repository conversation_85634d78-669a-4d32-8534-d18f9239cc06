package com.invest.trendanalysis.enumPackage;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor()
public enum SecCashFolwStatementsMetricEnum {
    NET_CASH_FROM_OPERATING_ACTIVITIES("NetCashFromOperatingActivities","经营活动产生的现金流量净额"),
    NET_CASH_FROM_INVESTING_ACTIVITIES("NetCashFromInvestingActivities","投资活动产生的现金流量净额"),
    PAYMENTS_FOR_PROPERTY_PLANT_AND_EQUIPMENT("PaymentsForPropertyPlantAndEquipment","购买固定资产支出"),
    PAYMENTS_TO_ACQUIRE_BUSINESSES("PaymentsToAcquireBusinesses","收购业务支付的现金"),
    PROCEEDS_FROM_MARKETABLE_SECURITIES("ProceedsFromMarketableSecurities","出售有价证券所得"),
    NET_CASH_FROM_FINANCING_ACTIVITIES("NetCashFromFinancingActivities","筹资活动产生的现金流量净额"),
    PROCEEDS_FROM_ISSUANCE_OF_DEBT("ProceedsFromIssuanceOfDebt","发行债务所得"),
    REPAYMENTS_OF_DEBT("RepaymentsOfDebt","偿还债务支付"),
    PAYMENTS_FOR_REPURCHASE_OF_COMMON_STOCK("PaymentsForRepurchaseOfCommonStock","回购普通股支付"),
    PAYMENTS_OF_DIVIDENDS("PaymentsOfDividends","支付股利"),
    PROCEEDS_FROM_ISSUANCE_OF_COMMON_STOCK("ProceedsFromIssuanceOfCommonStock","发行股票所得"),
    CASH_PERIOD_INCREASE_DECREASE("CashPeriodIncreaseDecrease","现金及现金等价物净增减额"),
    DEPRECIATION_DEPLETION_AND_AMORTIZATION_CASHFLOW("DepreciationDepletionAndAmortizationCashflow","折旧、损耗和摊销（加回）"),
    SHARE_BASED_COMPENSATION_CASHFLOW("ShareBasedCompensationCashflow","股权激励费用（加回）"),
    INCREASE_DECREASE_IN_ACCOUNTS_RECEIVABLE("IncreaseDecreaseInAccountsReceivable","应收账款的增减"),
    INCREASE_DECREASE_IN_INVENTORIES("IncreaseDecreaseInInventories","存货的增减"),
    INCREASE_DECREASE_IN_ACCOUNTS_PAYABLE("IncreaseDecreaseInAccountsPayable","应付账款的增减"),
    INCREASE_DECREASE_IN_CONTRACT_LIABILITY("IncreaseDecreaseInContractLiability","合同负债（递延收入）的增减");
    private final String metric;
    private final String metricDescription;
}