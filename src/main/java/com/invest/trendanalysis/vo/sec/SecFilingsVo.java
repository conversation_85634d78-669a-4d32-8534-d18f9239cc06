package com.invest.trendanalysis.vo.sec;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * SEC报告信息
 */
@Schema(description = "SEC报告信息")
@Data
@TableName(value = "trend.sec_filings")
public class SecFilingsVo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键")
    private Long id;

    /**
     * cik
     */
    @TableField(value = "cik")
    @Schema(description = "cik")
    private String cik;

    /**
     * 申报唯一标识符
     */
    @TableField(value = "accession_number")
    @Schema(description = "SEC访问号，每个提交文件的唯一标识符")
    private String accessionNumber;

    /**
     * 提交日期
     */
    @TableField(value = "filing_date")
    @Schema(description = "文件提交给SEC的日期")
    private String filingdate;

    /**
     * 截止日期
     */
    @TableField(value = "report_date")
    @Schema(description = "报告所涵盖的期间结束日期")
    private String reportdate;

    /**
     * 报告类型
     */
    @TableField(value = "form")
    @Schema(description = "SEC标准表格类型")
    private String form;

    /**
     * 文件名
     */
    @TableField(value = "primary_document")
    @Schema(description = "主要文档的文件名")
    private String primarydocument;

    /**
     * 文件类型
     */
    @TableField(value = "primary_doc_description")
    @Schema(description = "主要文档的描述")
    private String primarydocdescription;

    /**
     * SEC正式接受文件的精确时间戳
     */
    @TableField(value = "acceptance_date_time")
    @Schema(description = "SEC正式接受文件的精确时间戳")
    private String acceptanceDateTime;
}