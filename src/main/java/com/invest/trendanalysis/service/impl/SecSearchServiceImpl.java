package com.invest.trendanalysis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.invest.trendanalysis.service.SecCompanyTickersService;
import com.invest.trendanalysis.service.SecFinancialReportsService;
import com.invest.trendanalysis.service.SecSearchService;
import com.invest.trendanalysis.vo.SearchSymbolVo;
import com.invest.trendanalysis.vo.sec.SearchReportPageListConditionVo;
import com.invest.trendanalysis.vo.sec.SecCompanyTickersVo;
import com.invest.trendanalysis.vo.sec.SecReportDetailVo;
import com.invest.trendanalysis.vo.sec.SecReportPageListVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SecSearchServiceImpl implements SecSearchService {
    private final SecCompanyTickersService secCompanyTickersService;
    private final SecFinancialReportsService secFinancialReportsService;
    @Override
    public Page<SecCompanyTickersVo> searchCompanyTickers(SearchSymbolVo searchSymbolVo) {
        Page<SecCompanyTickersVo> secCompanyTickersVoPage = new Page<>();
        secCompanyTickersVoPage.setCurrent(searchSymbolVo.getCurrent());
        secCompanyTickersVoPage.setSize(searchSymbolVo.getSize());
        return secCompanyTickersService.getBaseMapper().selectPage(secCompanyTickersVoPage, new QueryWrapper<SecCompanyTickersVo>()
                .like("ticker", searchSymbolVo.getSymbol())
                .like("title", searchSymbolVo.getSymbol()));
    }

    @Override
    public Page<SecReportPageListVo> searchReportPageList(SearchReportPageListConditionVo searchReportPageListConditionVo) {
        return secFinancialReportsService.searchReportPageList(searchReportPageListConditionVo);
    }

    @Override
    public SecReportDetailVo queryReportDetailById(String reportId) {
         return secFinancialReportsService.queryReportDetailById(reportId);
    }

}