package com.invest.trendanalysis.vo.sec;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "报告列表信息")
public class SecReportPageListVo {
    @Schema(description = "报告ID")
    private String reportId;
    @Schema(description = "股票代码")
    private String ticker;
    @Schema(description = "报告类型 (如 10-K, 10-Q)")
    private String formType;
    @Schema(description = "报告截止日期")
    private String reportDate;
    @Schema(description = "sec访问号")
    private String accessionNumber;
}
