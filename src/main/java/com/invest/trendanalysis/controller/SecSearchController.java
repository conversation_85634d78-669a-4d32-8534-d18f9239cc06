package com.invest.trendanalysis.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.invest.trendanalysis.service.SecSearchService;
import com.invest.trendanalysis.vo.SearchSymbolVo;
import com.invest.trendanalysis.vo.sec.SearchReportPageListConditionVo;
import com.invest.trendanalysis.vo.sec.SecCompanyTickersVo;
import com.invest.trendanalysis.vo.sec.SecReportDetailVo;
import com.invest.trendanalysis.vo.sec.SecReportPageListVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * SEC检索接口
 */
@Slf4j
@Tag(name = "SEC检索接口")
@RestController
@RequestMapping("SecSearchApi")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SecSearchController {

    private final SecSearchService secSearchService;

    @PostMapping("searchCompanyTickersList")
    @Operation(summary = "根据标的名称或代码获取cik",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    content = @Content(
                            mediaType = "multipart/form-data",
                            schema = @Schema(implementation = SearchSymbolVo.class)
                    )
            ))
    public ResponseEntity<Page<SecCompanyTickersVo>> searchCompanyTickersList(SearchSymbolVo searchSymbolVo) {
        return ResponseEntity.ok(secSearchService.searchCompanyTickers(searchSymbolVo));
    }

    @PostMapping("searchReportPageList")
    @Operation(summary = "根据查询参数查询报告信息",
            requestBody = @io.swagger.v3.oas.annotations.parameters.RequestBody(
                    content = @Content(
                            mediaType = "multipart/form-data",
                            schema = @Schema(implementation = SearchReportPageListConditionVo.class)
                    )
            ))
    public ResponseEntity<Page<SecReportPageListVo>> searchReportPageList(SearchReportPageListConditionVo searchReportPageListConditionVo) {
        return ResponseEntity.ok(secSearchService.searchReportPageList(searchReportPageListConditionVo));
    }

    @GetMapping("queryReportDetailById")
    @Operation(summary = "根据报告ID查询报告详细信息")
    @Parameter(name = "reportId", in = ParameterIn.QUERY, description = "报告ID")
    ResponseEntity<SecReportDetailVo> queryReportDetailById(String reportId) {
        return ResponseEntity.ok(secSearchService.queryReportDetailById(reportId));
    }
}