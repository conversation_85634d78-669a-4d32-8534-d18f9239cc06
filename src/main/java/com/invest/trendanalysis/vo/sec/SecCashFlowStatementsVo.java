package com.invest.trendanalysis.vo.sec;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 现金流量表数据
 */
@Schema(description="现金流量表数据")
@Data
@TableName(value = "trend.sec_cash_flow_statements")
public class SecCashFlowStatementsVo {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    @Schema(description = "主键")
    private Integer id;

    /**
     * 关联financial_reports表ID
     */
    @TableField(value = "report_id")
    @Schema(description = "关联financial_reports表ID")
    private Long reportId;

    /**
     * 经营活动产生的现金流量净额
     */
    @TableField(value = "net_cash_from_operating_activities")
    @Schema(description = "经营活动产生的现金流量净额")
    private BigDecimal netCashFromOperatingActivities;

    /**
     * 投资活动产生的现金流量净额
     */
    @TableField(value = "net_cash_from_investing_activities")
    @Schema(description = "投资活动产生的现金流量净额")
    private BigDecimal netCashFromInvestingActivities;

    /**
     * 购买固定资产支出
     */
    @TableField(value = "payments_for_property_plant_and_equipment")
    @Schema(description = "购买固定资产支出")
    private BigDecimal paymentsForPropertyPlantAndEquipment;

    /**
     * 收购业务支付的现金
     */
    @TableField(value = "payments_to_acquire_businesses")
    @Schema(description = "收购业务支付的现金")
    private BigDecimal paymentsToAcquireBusinesses;

    /**
     * 出售有价证券所得
     */
    @TableField(value = "proceeds_from_marketable_securities")
    @Schema(description = "出售有价证券所得")
    private BigDecimal proceedsFromMarketableSecurities;

    /**
     * 筹资活动产生的现金流量净额
     */
    @TableField(value = "net_cash_from_financing_activities")
    @Schema(description = "筹资活动产生的现金流量净额")
    private BigDecimal netCashFromFinancingActivities;

    /**
     * 发行债务所得
     */
    @TableField(value = "proceeds_from_issuance_of_debt")
    @Schema(description = "发行债务所得")
    private BigDecimal proceedsFromIssuanceOfDebt;

    /**
     * 偿还债务支付
     */
    @TableField(value = "repayments_of_debt")
    @Schema(description = "偿还债务支付")
    private BigDecimal repaymentsOfDebt;

    /**
     * 回购普通股支付
     */
    @TableField(value = "payments_for_repurchase_of_common_stock")
    @Schema(description = "回购普通股支付")
    private BigDecimal paymentsForRepurchaseOfCommonStock;

    /**
     * 支付股利
     */
    @TableField(value = "payments_of_dividends")
    @Schema(description = "支付股利")
    private BigDecimal paymentsOfDividends;

    /**
     * 发行股票所得
     */
    @TableField(value = "proceeds_from_issuance_of_common_stock")
    @Schema(description = "发行股票所得")
    private BigDecimal proceedsFromIssuanceOfCommonStock;

    /**
     * 现金及现金等价物净增减额
     */
    @TableField(value = "cash_period_increase_decrease")
    @Schema(description = "现金及现金等价物净增减额")
    private BigDecimal cashPeriodIncreaseDecrease;

    /**
     * 折旧、损耗和摊销（加回）
     */
    @TableField(value = "depreciation_depletion_and_amortization_cashflow")
    @Schema(description = "折旧、损耗和摊销（加回）")
    private BigDecimal depreciationDepletionAndAmortizationCashflow;

    /**
     * 股权激励费用（加回）
     */
    @TableField(value = "share_based_compensation_cashflow")
    @Schema(description = "股权激励费用（加回）")
    private BigDecimal shareBasedCompensationCashflow;

    /**
     * 应收账款的增减
     */
    @TableField(value = "increase_decrease_in_accounts_receivable")
    @Schema(description = "应收账款的增减")
    private BigDecimal increaseDecreaseInAccountsReceivable;

    /**
     * 存货的增减
     */
    @TableField(value = "increase_decrease_in_inventories")
    @Schema(description = "存货的增减")
    private BigDecimal increaseDecreaseInInventories;

    /**
     * 应付账款的增减
     */
    @TableField(value = "increase_decrease_in_accounts_payable")
    @Schema(description = "应付账款的增减")
    private BigDecimal increaseDecreaseInAccountsPayable;

    /**
     * 合同负债（递延收入）的增减
     */
    @TableField(value = "increase_decrease_in_contract_liability")
    @Schema(description = "合同负债（递延收入）的增减")
    private BigDecimal increaseDecreaseInContractLiability;
    /**
     * sec唯一访问号
     */
    @TableField("accession_number")
    @Schema(description = "sec唯一访问号")
    private String accessionNumber;
    /**
     * 财年
     */
    @TableField("fiscal_year")
    @Schema(description = "财年")
    private String fiscalYear;

    /**
     * 财季
     */
    @TableField("fiscal_period")
    @Schema(description = "财季")
    private String fiscalPeriod;
}