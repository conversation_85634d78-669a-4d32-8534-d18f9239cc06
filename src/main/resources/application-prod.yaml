spring:
  application:
    name: TrendAnalysis
  ai:
    deepseek:
      api-key: 123
    vertex:
      ai:
        gemini:
          project-id: gen-lang-client-0263243361
          location: global
  output:
    ansi:
      enabled: always
  security:
    user:
      name: cvbnt
      password: Classpad330!
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: *********************************************************************************************************************************************
    username: root
    password: Classpad330!
  redis:
    redisson:
      config: |
        singleServerConfig:
          idleConnectionTimeout: 10000
          connectTimeout: 10000
          timeout: 3000
          retryAttempts: 3
          retryInterval: 1500
          password: 546549783
          subscriptionsPerConnection: 5
          clientName: null
          address: "redis://redis:6379"
          subscriptionConnectionMinimumIdleSize: 1
          subscriptionConnectionPoolSize: 50
          connectionMinimumIdleSize: 5
          connectionPoolSize: 64
          database: 0
          dnsMonitoringInterval: -1
        threads: 16
        nettyThreads: 32
        codec: !<org.redisson.codec.JsonJacksonCodec> {}
        transportMode: "NIO"
  rabbitmq:
    host: rabbitmq
    port: 5672
    virtual-host: /
    publisher-returns: true #发送端消息抵达队列确认
    listener:
      simple:
        acknowledge-mode: manual #手动ack消息
        default-requeue-rejected: false #拒绝的消息不需要排队，进入死信路由
    template:
      mandatory: true #只要抵达队列，以异步方式优先回调我们这个renturnConfirm
    username: cvbnt
    password: 546549783
mybatis-plus:
  mapper-locations: classpath:/mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
server:
  port: 8080
  ssl:
    enabled: true
    protocol: TLS
    certificate: file:/cert/fullchain.cer
    certificate-private-key: file:/cert/onelove.cloudns.ch.key
    trust-certificate: file:/cert/ca.cer
    enabled-protocols: TLSv1.2,TLSv1.3
    ciphers: >
      TLS_ECDHE_ECDSA_WITH_AES_128_GCM_SHA256,
      TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,
      TLS_AES_128_GCM_SHA256,
      TLS_AES_256_GCM_SHA384
springdoc:
  default-flat-param-object: false
  api-docs:
    enabled: true
    path: /v3/api-docs
    groups:
      enabled: true
  swagger-ui:
    enabled: true
    path: /index.html
    docExpansion: list
    default-model-expand-depth: 5
  packages-to-scan: com.invest.trendanalysis
xxl:
  job:
    accessToken: default_token
    admin:
      addresses: https://xxl-job-admin:8080/xxl-job-admin
    executor:
      address:
      appname: trendAnalysis
      ip:
      port: 9999
      logpath:
      logretentiondays: 30
longbridge:
  Mock: true