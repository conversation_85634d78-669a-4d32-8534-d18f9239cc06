<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.invest.trendanalysis.mapper.SecIncomeStatementsVoMapper">
  <resultMap id="BaseResultMap" type="com.invest.trendanalysis.vo.sec.SecIncomeStatementsVo">
    <!--@mbg.generated-->
    <!--@Table trend.sec_income_statements-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="report_id" jdbcType="INTEGER" property="reportId" />
    <result column="revenues" jdbcType="DECIMAL" property="revenues" />
    <result column="cost_of_revenue" jdbcType="DECIMAL" property="costOfRevenue" />
    <result column="gross_profit" jdbcType="DECIMAL" property="grossProfit" />
    <result column="research_and_development_expense" jdbcType="DECIMAL" property="researchAndDevelopmentExpense" />
    <result column="selling_general_and_administrative_expense" jdbcType="DECIMAL" property="sellingGeneralAndAdministrativeExpense" />
    <result column="operating_income_loss" jdbcType="DECIMAL" property="operatingIncomeLoss" />
    <result column="nonoperating_income_expense" jdbcType="DECIMAL" property="nonoperatingIncomeExpense" />
    <result column="interest_expense" jdbcType="DECIMAL" property="interestExpense" />
    <result column="investment_income_interest" jdbcType="DECIMAL" property="investmentIncomeInterest" />
    <result column="income_before_tax" jdbcType="DECIMAL" property="incomeBeforeTax" />
    <result column="income_tax_expense_benefit" jdbcType="DECIMAL" property="incomeTaxExpenseBenefit" />
    <result column="net_income_loss" jdbcType="DECIMAL" property="netIncomeLoss" />
    <result column="earnings_per_share_basic" jdbcType="DECIMAL" property="earningsPerShareBasic" />
    <result column="earnings_per_share_diluted" jdbcType="DECIMAL" property="earningsPerShareDiluted" />
    <result column="operating_expenses" jdbcType="DECIMAL" property="operatingExpenses" />
    <result column="depreciation_and_amortization" jdbcType="DECIMAL" property="depreciationAndAmortization" />
    <result column="share_based_compensation" jdbcType="DECIMAL" property="shareBasedCompensation" />
    <result column="gains_losses_on_extinguishment_of_debt" jdbcType="DECIMAL" property="gainsLossesOnExtinguishmentOfDebt" />
    <result column="gain_loss_on_investments" jdbcType="DECIMAL" property="gainLossOnInvestments" />
    <result column="comprehensive_income_net_of_tax" jdbcType="DECIMAL" property="comprehensiveIncomeNetOfTax" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, report_id, revenues, cost_of_revenue, gross_profit, research_and_development_expense,
    selling_general_and_administrative_expense, operating_income_loss, nonoperating_income_expense,
    interest_expense, investment_income_interest, income_before_tax, income_tax_expense_benefit,
    net_income_loss, earnings_per_share_basic, earnings_per_share_diluted, operating_expenses,
    depreciation_and_amortization, share_based_compensation, gains_losses_on_extinguishment_of_debt,
    gain_loss_on_investments, comprehensive_income_net_of_tax
  </sql>
</mapper>