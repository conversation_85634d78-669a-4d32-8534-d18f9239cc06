<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.invest.trendanalysis.mapper.SecFinancialReportsVoMapper">

    <select id="searchReportPageList" resultType="com.invest.trendanalysis.vo.sec.SecReportPageListVo">
        select a.ticker ticker,
        b.id reportId,
        b.report_date reportDate,
        b.form_type formType,
        b.accession_number accessionNumber
        from sec_company_tickers a
        left join sec_financial_reports b on a.cik_str = b.cik_str
        <where>
            <if test="vo.formType != null and vo.formType != ''">
                and b.form_type = #{vo.formType}
            </if>
            <if test="vo.startReportDate != null and vo.startReportDate != ''">
                and b.report_date >= date(#{vo.startReportDate})
            </if>
            <if test="vo.startReportDate != null and vo.startReportDate != ''">
                and b.report_date  <![CDATA[<]]>  date(#{vo.startReportDate}) + 1
            </if>
            <if test="vo.cik != null and vo.cik != ''">
                and b.cik_str = #{vo.cik}
            </if>
            <if test="vo.formTypeList != null and vo.formTypeList.size() > 0">
                and b.form_type in
                <foreach item="item" index="index" collection="vo.formTypeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by b.report_date desc
    </select>

    <!-- 根据reportId查询报告详细信息，包含四个财务表的数据 -->
    <resultMap id="SecReportDetailResultMap" type="com.invest.trendanalysis.vo.sec.SecReportDetailVo">
        <!-- 利润表数据 -->
        <association property="secIncomeStatementsVo" javaType="com.invest.trendanalysis.vo.sec.SecIncomeStatementsVo">
            <id column="income_id" property="id"/>
            <result column="income_report_id" property="reportId"/>
            <result column="revenues" property="revenues"/>
            <result column="cost_of_revenue" property="costOfRevenue"/>
            <result column="gross_profit" property="grossProfit"/>
            <result column="research_and_development_expense" property="researchAndDevelopmentExpense"/>
            <result column="selling_general_and_administrative_expense" property="sellingGeneralAndAdministrativeExpense"/>
            <result column="operating_income_loss" property="operatingIncomeLoss"/>
            <result column="nonoperating_income_expense" property="nonoperatingIncomeExpense"/>
            <result column="interest_expense" property="interestExpense"/>
            <result column="investment_income_interest" property="investmentIncomeInterest"/>
            <result column="income_before_tax" property="incomeBeforeTax"/>
            <result column="income_tax_expense_benefit" property="incomeTaxExpenseBenefit"/>
            <result column="net_income_loss" property="netIncomeLoss"/>
            <result column="earnings_per_share_basic" property="earningsPerShareBasic"/>
            <result column="earnings_per_share_diluted" property="earningsPerShareDiluted"/>
            <result column="operating_expenses" property="operatingExpenses"/>
            <result column="depreciation_and_amortization" property="depreciationAndAmortization"/>
            <result column="share_based_compensation" property="shareBasedCompensation"/>
            <result column="gains_losses_on_extinguishment_of_debt" property="gainsLossesOnExtinguishmentOfDebt"/>
            <result column="gain_loss_on_investments" property="gainLossOnInvestments"/>
            <result column="comprehensive_income_net_of_tax" property="comprehensiveIncomeNetOfTax"/>
        </association>

        <!-- 资产负债表数据 -->
        <association property="secBalanceSheetsVo" javaType="com.invest.trendanalysis.vo.sec.SecBalanceSheetsVo">
            <id column="balance_id" property="id"/>
            <result column="balance_report_id" property="reportId"/>
            <result column="assets" property="assets"/>
            <result column="assets_current" property="assetsCurrent"/>
            <result column="cash_and_cash_equivalents" property="cashAndCashEquivalents"/>
            <result column="marketable_securities_current" property="marketableSecuritiesCurrent"/>
            <result column="accounts_receivable_net_current" property="accountsReceivableNetCurrent"/>
            <result column="inventory_net" property="inventoryNet"/>
            <result column="noncurrent_assets" property="noncurrentAssets"/>
            <result column="property_plant_and_equipment_net" property="propertyPlantAndEquipmentNet"/>
            <result column="goodwill" property="goodwill"/>
            <result column="intangible_assets_net_excluding_goodwill" property="intangibleAssetsNetExcludingGoodwill"/>
            <result column="liabilities" property="liabilities"/>
            <result column="liabilities_current" property="liabilitiesCurrent"/>
            <result column="accounts_payable_current" property="accountsPayableCurrent"/>
            <result column="accrued_liabilities_current" property="accruedLiabilitiesCurrent"/>
            <result column="debt_current" property="debtCurrent"/>
            <result column="deferred_revenue_current" property="deferredRevenueCurrent"/>
            <result column="liabilities_noncurrent" property="liabilitiesNoncurrent"/>
            <result column="long_term_debt_noncurrent" property="longTermDebtNoncurrent"/>
            <result column="operating_lease_liability_noncurrent" property="operatingLeaseLiabilityNoncurrent"/>
            <result column="stockholders_equity" property="stockholdersEquity"/>
            <result column="retained_earnings_accumulated_deficit" property="retainedEarningsAccumulatedDeficit"/>
            <result column="additional_paid_in_capital" property="additionalPaidInCapital"/>
            <result column="treasury_stock_value" property="treasuryStockValue"/>
            <result column="deferred_tax_assets_net" property="deferredTaxAssetsNet"/>
            <result column="deferred_tax_liabilities_net" property="deferredTaxLiabilitiesNet"/>
            <result column="operating_lease_right_of_use_asset" property="operatingLeaseRightOfUseAsset"/>
            <result column="liabilities_and_stockholders_equity" property="liabilitiesAndStockholdersEquity"/>
        </association>

        <!-- 现金流量表数据 -->
        <association property="secCashFlowStatementsVo" javaType="com.invest.trendanalysis.vo.sec.SecCashFlowStatementsVo">
            <id column="cashflow_id" property="id"/>
            <result column="cashflow_report_id" property="reportId"/>
            <result column="net_cash_from_operating_activities" property="netCashFromOperatingActivities"/>
            <result column="net_cash_from_investing_activities" property="netCashFromInvestingActivities"/>
            <result column="payments_for_property_plant_and_equipment" property="paymentsForPropertyPlantAndEquipment"/>
            <result column="payments_to_acquire_businesses" property="paymentsToAcquireBusinesses"/>
            <result column="proceeds_from_marketable_securities" property="proceedsFromMarketableSecurities"/>
            <result column="net_cash_from_financing_activities" property="netCashFromFinancingActivities"/>
            <result column="proceeds_from_issuance_of_debt" property="proceedsFromIssuanceOfDebt"/>
            <result column="repayments_of_debt" property="repaymentsOfDebt"/>
            <result column="payments_for_repurchase_of_common_stock" property="paymentsForRepurchaseOfCommonStock"/>
            <result column="payments_of_dividends" property="paymentsOfDividends"/>
            <result column="proceeds_from_issuance_of_common_stock" property="proceedsFromIssuanceOfCommonStock"/>
            <result column="cash_period_increase_decrease" property="cashPeriodIncreaseDecrease"/>
            <result column="depreciation_depletion_and_amortization_cashflow" property="depreciationDepletionAndAmortizationCashflow"/>
            <result column="share_based_compensation_cashflow" property="shareBasedCompensationCashflow"/>
            <result column="increase_decrease_in_accounts_receivable" property="increaseDecreaseInAccountsReceivable"/>
            <result column="increase_decrease_in_inventories" property="increaseDecreaseInInventories"/>
            <result column="increase_decrease_in_accounts_payable" property="increaseDecreaseInAccountsPayable"/>
            <result column="increase_decrease_in_contract_liability" property="increaseDecreaseInContractLiability"/>
        </association>

        <!-- 股权与股东回报数据 -->
        <association property="secEquityAndShareholderReturnVo" javaType="com.invest.trendanalysis.vo.sec.SecEquityAndShareholderReturnVo">
            <id column="equity_id" property="id"/>
            <result column="equity_report_id" property="reportId"/>
            <result column="common_stock_shares_outstanding" property="commonStockSharesOutstanding"/>
            <result column="weighted_average_shares_basic" property="weightedAverageSharesBasic"/>
            <result column="weighted_average_shares_diluted" property="weightedAverageSharesDiluted"/>
            <result column="repurchase_authorized_amount" property="repurchaseAuthorizedAmount"/>
            <result column="repurchase_remaining_amount" property="repurchaseRemainingAmount"/>
            <result column="common_stock_shares_authorized" property="commonStockSharesAuthorized"/>
            <result column="equity_accession_number" property="accessionNumber"/>
            <result column="equity_fiscal_year" property="fiscalYear"/>
            <result column="equity_fiscal_period" property="fiscalPeriod"/>
        </association>
    </resultMap>

    <select id="queryReportDetailById" resultMap="SecReportDetailResultMap">
        SELECT sis.id                 as income_id,
               sis.report_id          as income_report_id,
               sis.revenues,
               sis.cost_of_revenue,
               sis.gross_profit,
               sis.research_and_development_expense,
               sis.selling_general_and_administrative_expense,
               sis.operating_income_loss,
               sis.nonoperating_income_expense,
               sis.interest_expense,
               sis.investment_income_interest,
               sis.income_before_tax,
               sis.income_tax_expense_benefit,
               sis.net_income_loss,
               sis.earnings_per_share_basic,
               sis.earnings_per_share_diluted,
               sis.operating_expenses,
               sis.depreciation_and_amortization,
               sis.share_based_compensation,
               sis.gains_losses_on_extinguishment_of_debt,
               sis.gain_loss_on_investments,
               sis.comprehensive_income_net_of_tax,
               sbs.id                 as balance_id,
               sbs.report_id          as balance_report_id,
               sbs.assets,
               sbs.assets_current,
               sbs.cash_and_cash_equivalents,
               sbs.marketable_securities_current,
               sbs.accounts_receivable_net_current,
               sbs.inventory_net,
               sbs.noncurrent_assets,
               sbs.property_plant_and_equipment_net,
               sbs.goodwill,
               sbs.intangible_assets_net_excluding_goodwill,
               sbs.liabilities,
               sbs.liabilities_current,
               sbs.accounts_payable_current,
               sbs.accrued_liabilities_current,
               sbs.debt_current,
               sbs.deferred_revenue_current,
               sbs.liabilities_noncurrent,
               sbs.long_term_debt_noncurrent,
               sbs.operating_lease_liability_noncurrent,
               sbs.stockholders_equity,
               sbs.retained_earnings_accumulated_deficit,
               sbs.additional_paid_in_capital,
               sbs.treasury_stock_value,
               sbs.deferred_tax_assets_net,
               sbs.deferred_tax_liabilities_net,
               sbs.operating_lease_right_of_use_asset,
               sbs.liabilities_and_stockholders_equity,
               scfs.id                as cashflow_id,
               scfs.report_id         as cashflow_report_id,
               scfs.net_cash_from_operating_activities,
               scfs.net_cash_from_investing_activities,
               scfs.payments_for_property_plant_and_equipment,
               scfs.payments_to_acquire_businesses,
               scfs.proceeds_from_marketable_securities,
               scfs.net_cash_from_financing_activities,
               scfs.proceeds_from_issuance_of_debt,
               scfs.repayments_of_debt,
               scfs.payments_for_repurchase_of_common_stock,
               scfs.payments_of_dividends,
               scfs.proceeds_from_issuance_of_common_stock,
               scfs.cash_period_increase_decrease,
               scfs.depreciation_depletion_and_amortization_cashflow,
               scfs.share_based_compensation_cashflow,
               scfs.increase_decrease_in_accounts_receivable,
               scfs.increase_decrease_in_inventories,
               scfs.increase_decrease_in_accounts_payable,
               scfs.increase_decrease_in_contract_liability,
               seshr.id               as equity_id,
               seshr.report_id        as equity_report_id,
               seshr.common_stock_shares_outstanding,
               seshr.weighted_average_shares_basic,
               seshr.weighted_average_shares_diluted,
               seshr.repurchase_authorized_amount,
               seshr.repurchase_remaining_amount,
               seshr.common_stock_shares_authorized,
               seshr.accession_number as equity_accession_number,
               seshr.fiscal_year      as equity_fiscal_year,
               seshr.fiscal_period    as equity_fiscal_period
        FROM trend.sec_financial_reports sfr
                 LEFT JOIN trend.sec_income_statements sis ON sfr.id = sis.report_id
                 LEFT JOIN trend.sec_balance_sheets sbs ON sfr.id = sbs.report_id
                 LEFT JOIN trend.sec_cash_flow_statements scfs ON sfr.id = scfs.report_id
                 LEFT JOIN trend.sec_equity_and_shareholder_return seshr ON sfr.id = seshr.report_id
        WHERE sfr.id = #{reportId}
    </select>
</mapper>