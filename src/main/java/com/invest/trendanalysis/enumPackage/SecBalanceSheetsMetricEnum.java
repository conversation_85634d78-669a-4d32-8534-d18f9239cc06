package com.invest.trendanalysis.enumPackage;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor()
public enum SecBalanceSheetsMetricEnum {
    ASSETS("Assets","总资产"),
    ASSETS_CURRENT("AssetsCurrent","流动资产"),
    CASH_AND_CASH_EQUIVALENTS("CashAndCashEquivalents","现金及现金等价物"),
    MARKETABLE_SECURITIES_CURRENT(" MarketableSecuritiesCurrent","短期有价证券"),
    ACCOUNTS_RECEIVABLE_NET_CURRENT("AccountsReceivableNetCurrent","应收账款净额"),
    INVENTORY_NET("InventoryNet","存货净额"),
    NONCURRENT_ASSETS("NoncurrentAssets","非流动资产"),
    PROPERTY_PLANT_AND_EQUIPMENT_NET("PropertyPlantAndEquipmentNet","固定资产净额"),
    GOODWILL("Goodwill","商誉"),
    INTANGIBLE_ASSETS_NET_EXCLUDING_GOODWILL("IntangibleAssetsNetExcludingGoodwill","无形资产净额（除商誉）"),
    LIABILITIES("Liabilities","总负债"),
    LIABILITIES_CURRENT("LiabilitiesCurrent","流动负债"),
    ACCOUNTS_PAYABLE_CURRENT("AccountsPayableCurrent","应付账款"),
    ACCRUED_LIABILITIES_CURRENT("AccruedLiabilitiesCurrent","应计负债"),
    DEBT_CURRENT("DebtCurrent","短期债务"),
    DEFERRED_REVENUE_CURRENT("DeferredRevenueCurrent","短期递延收入"),
    LIABILITIES_NONCURRENT("LiabilitiesNoncurrent","非流动负债"),
    LONG_TERM_DEBT_NONCURRENT("LongTermDebtNoncurrent","长期债务"),
    OPERATING_LEASE_LIABILITY_NONCURRENT("OperatingLeaseLiabilityNoncurrent","长期经营租赁负债"),
    STOCKHOLDERS_EQUITY("StockholdersEquity","股东权益"),
    RETAINED_EARNINGS_ACCUMULATED_DEFICIT("RetainedEarningsAccumulatedDeficit","留存收益"),
    ADDITIONAL_PAID_IN_CAPITAL("AdditionalPaidInCapital","额外实收资本"),
    TREASURY_STOCK_VALUE("TreasuryStockValue","库存股价值"),
    DEFERRED_TAX_ASSETS_NET("DeferredTaxAssetsNet","递延所得税资产净额"),
    DEFERRED_TAX_LIABILITIES_NET("DeferredTaxLiabilitiesNet","递延所得税负债净额"),
    OPERATING_LEASE_RIGHT_OF_USE_ASSET("OperatingLeaseRightOfUseAsset","经营租赁使用权资产"),
    LIABILITIES_AND_STOCKHOLDERS_EQUITY("LiabilitiesAndStockholdersEquity","负债和股东权益总计");
    private final String metric;
    private final String metricDescription;
}