package com.invest.trendanalysis.vo.sec;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "查询报告列表信息参数")
public class SearchReportPageListConditionVo {
    @Schema(description = "cik")
    private String cik;
    @Schema(description = "报告开始日期，yyyy-MM-dd")
    private String startReportDate;
    @Schema(description = "报告截至日期，yyyy-MM-dd")
    private String endReportDate;
    @Schema(description = "报告类型 (如 10-K, 10-Q)")
    private String formType;
    @Schema(description = "多种报告类型")
    private List<String> formTypeList;
    @Schema(description = "sec访问号")
    private String accessionNumber;
    @Schema(description = "当前页")
    private Integer current;
    @Schema(description = "条目数")
    private Integer size;
}
