package com.invest.trendanalysis.vo.sec;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "报告详细信息")
public class SecReportDetailVo {
    @Schema(description = "利润表数据")
    private SecIncomeStatementsVo secIncomeStatementsVo;
    @Schema(description = "资产负债表数据")
    private SecBalanceSheetsVo secBalanceSheetsVo;
    @Schema(description = "现金流量表数据")
    private SecCashFlowStatementsVo secCashFlowStatementsVo;
    @Schema(description = "股权与股东回报数据")
    private SecEquityAndShareholderReturnVo secEquityAndShareholderReturnVo;
}