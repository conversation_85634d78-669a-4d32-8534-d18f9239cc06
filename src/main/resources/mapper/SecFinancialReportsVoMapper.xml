<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.invest.trendanalysis.mapper.SecFinancialReportsVoMapper">

    <select id="searchReportPageList" resultType="com.invest.trendanalysis.vo.sec.SecReportPageListVo">
        select a.ticker ticker,
        b.id reportId,
        b.report_date reportDate,
        b.form_type formType,
        b.accession_number accessionNumber
        from sec_company_tickers a
        left join sec_financial_reports b on a.cik_str = b.cik_str
        <where>
            <if test="vo.formType != null and vo.formType != ''">
                and b.form_type = #{vo.formType}
            </if>
            <if test="vo.startDate != null and vo.startDate != ''">
                and b.report_date > date(#{vo.startReportDate})
            </if>
            <if test="vo.endDate != null and vo.endDate != ''">
                and b.report_date  <![CDATA[<]]>  date(#{vo.endReportDate}) + 1
            </if>
            <if test="vo.cik != null and vo.cik != ''">
                and b.cik_str = #{vo.cik}
            </if>
            <if test="vo.formTypeList != null and vo.formTypeList.size() > 0">
                and b.form_type in
                <foreach item="item" index="index" collection="vo.formTypeList" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        order by b.report_date desc
    </select>
</mapper>