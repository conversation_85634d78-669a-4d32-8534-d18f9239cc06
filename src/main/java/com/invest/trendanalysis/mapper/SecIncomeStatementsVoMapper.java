package com.invest.trendanalysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.invest.trendanalysis.vo.sec.SecIncomeStatementsVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SecIncomeStatementsVoMapper extends BaseMapper<SecIncomeStatementsVo> {
    void removeByCikAndFormList(@Param("cik") String cik, @Param("formList") List<String> formList);
}