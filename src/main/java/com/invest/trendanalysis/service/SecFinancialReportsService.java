package com.invest.trendanalysis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.invest.trendanalysis.vo.sec.SearchReportPageListConditionVo;
import com.invest.trendanalysis.vo.sec.SecFinancialReportsVo;
import com.invest.trendanalysis.vo.sec.SecReportPageListVo;


public interface SecFinancialReportsService extends IService<SecFinancialReportsVo> {
    Page<SecReportPageListVo> searchReportPageList(SearchReportPageListConditionVo searchReportPageListConditionVo);
}
