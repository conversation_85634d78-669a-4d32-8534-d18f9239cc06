package com.invest.trendanalysis;

import com.invest.trendanalysis.mapper.XxlJobInfoMapper;
import com.invest.trendanalysis.service.CandlestickService;
import com.invest.trendanalysis.service.SecDataService;
import com.invest.trendanalysis.service.Service;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.redisson.api.RedissonClient;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.vertexai.gemini.VertexAiGeminiChatModel;
import org.springframework.ai.vertexai.gemini.VertexAiGeminiChatOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = TrendAnalysisApplication.class)
@RunWith(SpringRunner.class)
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
class TrendAnalysisApplicationTests {
    private final XxlJobInfoMapper xxlJobInfoMapper;
    private final RedissonClient redissonClient;
    private final Service service;
    private final CandlestickService candlestickService;
    private final SecDataService secDataService;
    private final VertexAiGeminiChatModel vertexAiGeminiChatModel;
    @Test
    void contextLoads() throws Exception {
        ChatResponse chatResponse = vertexAiGeminiChatModel.call(new Prompt("Generate the names of 5 famous pirates.",
                VertexAiGeminiChatOptions.builder()
                        .temperature(0.4)
                        .build()));
        System.out.println(chatResponse);
    }
}