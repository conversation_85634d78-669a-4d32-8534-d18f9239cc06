package com.invest.trendanalysis.controller;

import com.invest.trendanalysis.service.SecDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * SEC数据接口
 */
@Slf4j
@Tag(name = "SEC数据接口")
@RestController
@RequestMapping("SecDataApi")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SecDataController {
    private final SecDataService secDataService;

    @GetMapping("updateSecGappLabelByCik")
    @Operation(summary = "根据CIK更新报告数据")
    @Parameter(name = "cik", in = ParameterIn.QUERY, description = "cik")
    public ResponseEntity<String> updateSecGappLabelByCik(String cik) throws Exception {
        secDataService.updateSecGappLabelByCik(cik);
        return ResponseEntity.ok("更新成功");
    }
}