package com.invest.trendanalysis.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.invest.trendanalysis.mapper.SecFinancialReportsVoMapper;
import com.invest.trendanalysis.service.SecFinancialReportsService;
import com.invest.trendanalysis.vo.sec.SearchReportPageListConditionVo;
import com.invest.trendanalysis.vo.sec.SecFinancialReportsVo;
import com.invest.trendanalysis.vo.sec.SecReportDetailVo;
import com.invest.trendanalysis.vo.sec.SecReportPageListVo;
import org.springframework.stereotype.Service;

@Service
public class SecFinancialReportsServiceImpl extends ServiceImpl<SecFinancialReportsVoMapper, SecFinancialReportsVo> implements SecFinancialReportsService {
    @Override
    public Page<SecReportPageListVo> searchReportPageList(SearchReportPageListConditionVo searchReportPageListConditionVo) {
        Page<SecReportPageListVo> page = new Page<>();
        page.setCurrent(searchReportPageListConditionVo.getCurrent());
        page.setSize(searchReportPageListConditionVo.getSize());
        return baseMapper.searchReportPageList(page, searchReportPageListConditionVo);
    }

    @Override
    public SecReportDetailVo queryReportDetailById(String reportId) {
        return baseMapper.queryReportDetailById(reportId);
    }
}