<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.invest.trendanalysis.mapper.SecBalanceSheetsVoMapper">
  <resultMap id="BaseResultMap" type="com.invest.trendanalysis.vo.sec.SecBalanceSheetsVo">
    <!--@mbg.generated-->
    <!--@Table trend.sec_balance_sheets-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="report_id" jdbcType="INTEGER" property="reportId" />
    <result column="assets" jdbcType="DECIMAL" property="assets" />
    <result column="assets_current" jdbcType="DECIMAL" property="assetsCurrent" />
    <result column="cash_and_cash_equivalents" jdbcType="DECIMAL" property="cashAndCashEquivalents" />
    <result column="marketable_securities_current" jdbcType="DECIMAL" property="marketableSecuritiesCurrent" />
    <result column="accounts_receivable_net_current" jdbcType="DECIMAL" property="accountsReceivableNetCurrent" />
    <result column="inventory_net" jdbcType="DECIMAL" property="inventoryNet" />
    <result column="noncurrent_assets" jdbcType="DECIMAL" property="noncurrentAssets" />
    <result column="property_plant_and_equipment_net" jdbcType="DECIMAL" property="propertyPlantAndEquipmentNet" />
    <result column="goodwill" jdbcType="DECIMAL" property="goodwill" />
    <result column="intangible_assets_net_excluding_goodwill" jdbcType="DECIMAL" property="intangibleAssetsNetExcludingGoodwill" />
    <result column="liabilities" jdbcType="DECIMAL" property="liabilities" />
    <result column="liabilities_current" jdbcType="DECIMAL" property="liabilitiesCurrent" />
    <result column="accounts_payable_current" jdbcType="DECIMAL" property="accountsPayableCurrent" />
    <result column="accrued_liabilities_current" jdbcType="DECIMAL" property="accruedLiabilitiesCurrent" />
    <result column="debt_current" jdbcType="DECIMAL" property="debtCurrent" />
    <result column="deferred_revenue_current" jdbcType="DECIMAL" property="deferredRevenueCurrent" />
    <result column="liabilities_noncurrent" jdbcType="DECIMAL" property="liabilitiesNoncurrent" />
    <result column="long_term_debt_noncurrent" jdbcType="DECIMAL" property="longTermDebtNoncurrent" />
    <result column="operating_lease_liability_noncurrent" jdbcType="DECIMAL" property="operatingLeaseLiabilityNoncurrent" />
    <result column="stockholders_equity" jdbcType="DECIMAL" property="stockholdersEquity" />
    <result column="retained_earnings_accumulated_deficit" jdbcType="DECIMAL" property="retainedEarningsAccumulatedDeficit" />
    <result column="additional_paid_in_capital" jdbcType="DECIMAL" property="additionalPaidInCapital" />
    <result column="treasury_stock_value" jdbcType="DECIMAL" property="treasuryStockValue" />
    <result column="deferred_tax_assets_net" jdbcType="DECIMAL" property="deferredTaxAssetsNet" />
    <result column="deferred_tax_liabilities_net" jdbcType="DECIMAL" property="deferredTaxLiabilitiesNet" />
    <result column="operating_lease_right_of_use_asset" jdbcType="DECIMAL" property="operatingLeaseRightOfUseAsset" />
    <result column="liabilities_and_stockholders_equity" jdbcType="DECIMAL" property="liabilitiesAndStockholdersEquity" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, report_id, assets, assets_current, cash_and_cash_equivalents, marketable_securities_current,
    accounts_receivable_net_current, inventory_net, noncurrent_assets, property_plant_and_equipment_net,
    goodwill, intangible_assets_net_excluding_goodwill, liabilities, liabilities_current,
    accounts_payable_current, accrued_liabilities_current, debt_current, deferred_revenue_current,
    liabilities_noncurrent, long_term_debt_noncurrent, operating_lease_liability_noncurrent,
    stockholders_equity, retained_earnings_accumulated_deficit, additional_paid_in_capital,
    treasury_stock_value, deferred_tax_assets_net, deferred_tax_liabilities_net, operating_lease_right_of_use_asset,
    liabilities_and_stockholders_equity
  </sql>
</mapper>