package com.invest.trendanalysis.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.invest.trendanalysis.vo.sec.SearchReportPageListConditionVo;
import com.invest.trendanalysis.vo.sec.SecFinancialReportsVo;
import com.invest.trendanalysis.vo.sec.SecReportDetailVo;
import com.invest.trendanalysis.vo.sec.SecReportPageListVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface SecFinancialReportsVoMapper extends BaseMapper<SecFinancialReportsVo> {
    Page<SecReportPageListVo> searchReportPageList(Page<SecReportPageListVo> page, @Param("vo") SearchReportPageListConditionVo searchReportPageListConditionVo);

}