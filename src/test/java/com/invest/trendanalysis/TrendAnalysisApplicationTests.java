package com.invest.trendanalysis;

import com.invest.trendanalysis.mapper.XxlJobInfoMapper;
import com.invest.trendanalysis.service.CandlestickService;
import com.invest.trendanalysis.service.SecService;
import com.invest.trendanalysis.service.Service;
import lombok.RequiredArgsConstructor;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = TrendAnalysisApplication.class)
@RunWith(SpringRunner.class)
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
class TrendAnalysisApplicationTests {
    private final XxlJobInfoMapper xxlJobInfoMapper;
    private final RedissonClient redissonClient;
    private final Service service;
    private final CandlestickService candlestickService;
    private final SecService secService;
    @Test
    void contextLoads() throws Exception {
        secService.updateSecGappLabel("1045810");
    }
}