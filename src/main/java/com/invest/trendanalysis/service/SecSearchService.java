package com.invest.trendanalysis.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.invest.trendanalysis.vo.SearchSymbolVo;
import com.invest.trendanalysis.vo.sec.SearchReportPageListConditionVo;
import com.invest.trendanalysis.vo.sec.SecCompanyTickersVo;
import com.invest.trendanalysis.vo.sec.SecReportDetailVo;
import com.invest.trendanalysis.vo.sec.SecReportPageListVo;

public interface SecSearchService {
    Page<SecCompanyTickersVo> searchCompanyTickers(SearchSymbolVo searchSymbolVo);

    Page<SecReportPageListVo> searchReportPageList(SearchReportPageListConditionVo searchReportPageListConditionVo);

    SecReportDetailVo queryReportDetailById(String reportId);
}