<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.invest.trendanalysis.mapper.SecCashFlowStatementsVoMapper">
  <resultMap id="BaseResultMap" type="com.invest.trendanalysis.vo.sec.SecCashFlowStatementsVo">
    <!--@mbg.generated-->
    <!--@Table trend.sec_cash_flow_statements-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="report_id" jdbcType="INTEGER" property="reportId" />
    <result column="net_cash_from_operating_activities" jdbcType="DECIMAL" property="netCashFromOperatingActivities" />
    <result column="net_cash_from_investing_activities" jdbcType="DECIMAL" property="netCashFromInvestingActivities" />
    <result column="payments_for_property_plant_and_equipment" jdbcType="DECIMAL" property="paymentsForPropertyPlantAndEquipment" />
    <result column="payments_to_acquire_businesses" jdbcType="DECIMAL" property="paymentsToAcquireBusinesses" />
    <result column="proceeds_from_marketable_securities" jdbcType="DECIMAL" property="proceedsFromMarketableSecurities" />
    <result column="net_cash_from_financing_activities" jdbcType="DECIMAL" property="netCashFromFinancingActivities" />
    <result column="proceeds_from_issuance_of_debt" jdbcType="DECIMAL" property="proceedsFromIssuanceOfDebt" />
    <result column="repayments_of_debt" jdbcType="DECIMAL" property="repaymentsOfDebt" />
    <result column="payments_for_repurchase_of_common_stock" jdbcType="DECIMAL" property="paymentsForRepurchaseOfCommonStock" />
    <result column="payments_of_dividends" jdbcType="DECIMAL" property="paymentsOfDividends" />
    <result column="proceeds_from_issuance_of_common_stock" jdbcType="DECIMAL" property="proceedsFromIssuanceOfCommonStock" />
    <result column="cash_period_increase_decrease" jdbcType="DECIMAL" property="cashPeriodIncreaseDecrease" />
    <result column="depreciation_depletion_and_amortization_cashflow" jdbcType="DECIMAL" property="depreciationDepletionAndAmortizationCashflow" />
    <result column="share_based_compensation_cashflow" jdbcType="DECIMAL" property="shareBasedCompensationCashflow" />
    <result column="increase_decrease_in_accounts_receivable" jdbcType="DECIMAL" property="increaseDecreaseInAccountsReceivable" />
    <result column="increase_decrease_in_inventories" jdbcType="DECIMAL" property="increaseDecreaseInInventories" />
    <result column="increase_decrease_in_accounts_payable" jdbcType="DECIMAL" property="increaseDecreaseInAccountsPayable" />
    <result column="increase_decrease_in_contract_liability" jdbcType="DECIMAL" property="increaseDecreaseInContractLiability" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, report_id, net_cash_from_operating_activities, net_cash_from_investing_activities,
    payments_for_property_plant_and_equipment, payments_to_acquire_businesses, proceeds_from_marketable_securities,
    net_cash_from_financing_activities, proceeds_from_issuance_of_debt, repayments_of_debt,
    payments_for_repurchase_of_common_stock, payments_of_dividends, proceeds_from_issuance_of_common_stock,
    cash_period_increase_decrease, depreciation_depletion_and_amortization_cashflow,
    share_based_compensation_cashflow, increase_decrease_in_accounts_receivable, increase_decrease_in_inventories,
    increase_decrease_in_accounts_payable, increase_decrease_in_contract_liability
  </sql>
</mapper>