package com.invest.trendanalysis.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.invest.trendanalysis.enumPackage.SecBalanceSheetsMetricEnum;
import com.invest.trendanalysis.enumPackage.SecCashFolwStatementsMetricEnum;
import com.invest.trendanalysis.enumPackage.SecEquityAndShareholderReturnMetricEnum;
import com.invest.trendanalysis.enumPackage.SecIncomeStatementsMetricEnum;
import com.invest.trendanalysis.service.*;
import com.invest.trendanalysis.vo.RecentFilingsVo;
import com.invest.trendanalysis.vo.sec.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatusCode;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestClient;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.invest.trendanalysis.enumPackage.SecApiEnum.*;
import static com.invest.trendanalysis.enumPackage.SecFormEnum.*;

@Slf4j
@Service("SercService")
@RequiredArgsConstructor(onConstructor = @__(@Autowired))
public class SecDataServiceImpl implements SecDataService {
    private final SecCompanyTickersService secCompanyTickersService;
    private final SecFilingsService secFilingsService;
    private final SecFinancialReportsService secFinancialReportsService;
    private final SecIncomeStatementsVoService secIncomeStatementsVoService;
    private final SecBalanceSheetsVoService secBalanceSheetsVoService;
    private final SecCashFlowStatementsVoService secCashFlowStatementsVoService;
    private final SecEquityAndShareholderReturnVoService secEquityAndShareholderReturnVoService;
    private final ObjectMapper objectMapper;
    @Value("${userAgent}")
    String userAgent;

    /**
     * curl --request GET
     * --url https://www.sec.gov/files/company_tickers.json
     * --header 'User-Agent: ___'
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSecCompanyTickers() {
        RestClient restClient = RestClient.builder()
                .baseUrl(SEC_COMPANY_TICKERS.getUrl())
                .defaultHeader("User-Agent", userAgent)
                .build();

        try {
            String jsonString = restClient.get()
                    .retrieve()
                    .onStatus(HttpStatusCode::isError, (request, response) -> {
                        log.error("调用{}失败,status code: {}, body: {}", SEC_COMPANY_TICKERS.getUrl(), response.getStatusCode(), new String(response.getBody().readAllBytes()));
                        throw new RuntimeException("SEC API call failed with status " + response.getStatusCode());
                    })
                    .body(String.class);

            Map<String, SecCompanyTickersVo> companyTickersMap = objectMapper.readValue(jsonString, new TypeReference<>() {
            });
            secCompanyTickersService.getBaseMapper().delete(null);
            secCompanyTickersService.saveBatch(new ArrayList<>(companyTickersMap.values()));
            log.info("成功写入CIK数据");
        } catch (JsonProcessingException e) {
            log.error("无法解析JSON数据", e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("调用SEC API时发生未知错误", e);
            throw new RuntimeException(e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateSecCompanyFilings(String cik) {
        String formattedCik = String.format("%010d", Integer.parseInt(cik));
        String fullUrl = SEC_SUBMISSIONS.getUrl() + formattedCik + ".json";

        RestClient restClient = RestClient.builder()
                .defaultHeader("User-Agent", userAgent)
                .build();

        try {
            String jsonString = restClient.get()
                    .uri(fullUrl)
                    .retrieve()
                    .onStatus(HttpStatusCode::isError, (request, response) -> {
                        log.error("调用{}失败,status code: {}, body: {}", fullUrl, response.getStatusCode(), new String(response.getBody().readAllBytes()));
                        throw new RuntimeException("SEC API call failed with status " + response.getStatusCode());
                    })
                    .body(String.class);

            JsonNode rootNode = objectMapper.readTree(jsonString);
            RecentFilingsVo recentFilings = objectMapper.treeToValue(rootNode.get("filings").get("recent"), RecentFilingsVo.class);
            List<SecFilingsVo> filings = IntStream.range(0, recentFilings.getAccessionNumber().size())
                    .mapToObj(i -> {
                        SecFilingsVo vo = new SecFilingsVo();
                        vo.setCik(cik);
                        vo.setAccessionNumber(recentFilings.getAccessionNumber().get(i));
                        vo.setFilingdate(recentFilings.getFilingDate().get(i));
                        vo.setReportdate(recentFilings.getReportDate().get(i));
                        vo.setForm(recentFilings.getForm().get(i));
                        vo.setPrimarydocument(recentFilings.getPrimaryDocument().get(i));
                        vo.setPrimarydocdescription(recentFilings.getPrimaryDocDescription().get(i));
                        vo.setAcceptanceDateTime(recentFilings.getAcceptanceDateTime().get(i));
                        return vo;
                    })
                    .collect(Collectors.toList());

            if (!filings.isEmpty()) {
                secFilingsService.getBaseMapper().delete(new QueryWrapper<SecFilingsVo>().eq("cik", cik));
                secFilingsService.saveBatch(filings);
                log.info("成功保存{}条报告， CIK：{}", filings.size(), cik);
            }

        } catch (JsonProcessingException e) {
            log.error("JSON数据解析错误，CIK {}", cik, e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("调用SEC API时发生未知错误，CIK {}", cik, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void printSecGappLabel(String cik) {
        String formattedCik = "CIK" + String.format("%010d", Integer.parseInt(cik));
        String fullUrl = SEC_XBRL_COMPANYFACTS.getUrl().replace("CIK", "") + formattedCik + ".json";

        RestClient restClient = RestClient.builder()
                .defaultHeader("User-Agent", userAgent)
                .build();
        try {
            String jsonString = restClient.get()
                    .uri(fullUrl)
                    .retrieve()
                    .onStatus(HttpStatusCode::isError, (request, response) -> {
                        log.error("调用{}失败,status code: {}, body: {}", fullUrl, response.getStatusCode(), new String(response.getBody().readAllBytes()));
                        throw new RuntimeException("SEC API call failed with status " + response.getStatusCode());
                    })
                    .body(String.class);

            JsonNode rootNode = objectMapper.readTree(jsonString);
            JsonNode usGaapNode = rootNode.path("facts").path("us-gaap");

            if (usGaapNode.isMissingNode() || !usGaapNode.isObject()) {
                log.warn("在CIK {}的返回数据中没有找到 'us-gaap' facts。", cik);
                return;
            }

            System.out.printf("""
                    ==================================================
                    公司财务指标: %s
                    ==================================================
                    """, rootNode.path("entityName").asText());

            int metricCount = 0;
            Iterator<String> fieldNames = usGaapNode.fieldNames();
            while (fieldNames.hasNext()) {
                String metricName = fieldNames.next();
                JsonNode metricDetails = usGaapNode.get(metricName);
                String label = metricDetails.path("label").asText();
                String description = metricDetails.path("description").asText();

                System.out.printf("""
                        指标名称: %s
                          - 标签: %s
                          - 描述: %s
                        --------------------------------------------------
                        """, metricName, label, description);
                metricCount++;
            }

            System.out.printf("""
                    ==================================================
                    总共找到 %d 条指标。
                    ==================================================
                    """, metricCount);

        } catch (JsonProcessingException e) {
            log.error("JSON数据解析错误，CIK {}", cik, e);
            throw new RuntimeException(e);
        } catch (Exception e) {
            log.error("调用SEC API时发生未知错误，CIK {}", cik, e);
            throw new RuntimeException(e);
        }
    }

    @Override
    public void updateSecGappLabelByCik(String cik) throws JsonProcessingException {
        List<String> formList = new ArrayList<>();
        formList.add(SEC_FORM_10Q.getFormType());
        formList.add(SEC_FORM_10QA.getFormType());
        formList.add(SEC_FORM_10K.getFormType());
        formList.add(SEC_FORM_10KA.getFormType());
        formList.add(SEC_FORM_8K.getFormType());
        formList.add(SEC_FORM_8KA.getFormType());
        List<SecFilingsVo> list = secFilingsService.getBaseMapper().selectList(new QueryWrapper<SecFilingsVo>().eq("cik", cik).in("form", formList));
        ArrayList<SecFinancialReportsVo> secFinancialReportsVos = new ArrayList<>();
        list.forEach(item -> {
            SecFinancialReportsVo secFinancialReportsVo = new SecFinancialReportsVo();
            secFinancialReportsVo.setCikStr(cik);
            secFinancialReportsVo.setAccessionNumber(item.getAccessionNumber());
            secFinancialReportsVo.setReportDate(LocalDate.parse(item.getReportdate(), DateTimeFormatter.ISO_LOCAL_DATE));
            secFinancialReportsVo.setFormType(item.getForm());
            secFinancialReportsVo.setFiledDate(LocalDate.parse(item.getFilingdate(), DateTimeFormatter.ISO_LOCAL_DATE));
            secFinancialReportsVos.add(secFinancialReportsVo);
        });
        secFinancialReportsService.remove(new QueryWrapper<SecFinancialReportsVo>().eq("cik_str", cik).in("form_type", formList));
        secFinancialReportsService.saveBatch(secFinancialReportsVos);
        String formattedCik = "CIK" + String.format("%010d", Integer.parseInt(cik));
        String fullUrl = SEC_XBRL_COMPANYFACTS.getUrl().replace("CIK", "") + formattedCik + ".json";
        RestClient restClient = RestClient.builder()
                .baseUrl(fullUrl)
                .defaultHeader("User-Agent", userAgent)
                .build();
        String jsonString = restClient.get()
                .retrieve()
                .onStatus(HttpStatusCode::isError, (request, response) -> {
                    log.error("调用{}失败,status code: {}, body: {}", fullUrl, response.getStatusCode(), new String(response.getBody().readAllBytes()));
                    throw new RuntimeException("SEC API call failed with status " + response.getStatusCode());
                })
                .body(String.class);
        JsonNode rootNode = objectMapper.readTree(jsonString);
        JsonNode usGaapNode = rootNode.path("facts").path("us-gaap");
        CompletableFuture<List<SecIncomeStatementsVo>> incomeStatementsFuture = CompletableFuture.supplyAsync(() -> processIncomeStatementsMetrics(usGaapNode, secFinancialReportsVos));
        CompletableFuture<List<SecBalanceSheetsVo>> balanceSheetsFuture = CompletableFuture.supplyAsync(() -> processBalanceSheetsMetrics(usGaapNode, secFinancialReportsVos));
        CompletableFuture<List<SecCashFlowStatementsVo>> cashFlowStatementsFuture = CompletableFuture.supplyAsync(() -> processCashFlowStatementsMetrics(usGaapNode, secFinancialReportsVos));
        CompletableFuture<List<SecEquityAndShareholderReturnVo>> equityAndShareholderReturnFuture = CompletableFuture.supplyAsync(() -> processEquityAndShareholderReturnMetrics(usGaapNode, secFinancialReportsVos));
        CompletableFuture.allOf(incomeStatementsFuture, balanceSheetsFuture, cashFlowStatementsFuture, equityAndShareholderReturnFuture).join();

        List<SecIncomeStatementsVo> secIncomeStatementsVos = incomeStatementsFuture.join();
        List<SecBalanceSheetsVo> secBalanceSheetsVos = balanceSheetsFuture.join();
        List<SecCashFlowStatementsVo> secCashFlowStatementsVos = cashFlowStatementsFuture.join();
        List<SecEquityAndShareholderReturnVo> secEquityAndShareholderReturnVos = equityAndShareholderReturnFuture.join();

        if (!secIncomeStatementsVos.isEmpty()) {
            secIncomeStatementsVoService.remove(new QueryWrapper<SecIncomeStatementsVo>().in("accession_number", secFinancialReportsVos.stream().map(SecFinancialReportsVo::getAccessionNumber).collect(Collectors.toList())));
            secIncomeStatementsVoService.saveBatch(secIncomeStatementsVos);
            log.info("成功插入 {} 条利润表数据", secIncomeStatementsVos.size());
        }
        if (!secBalanceSheetsVos.isEmpty()) {
            secBalanceSheetsVoService.remove(new QueryWrapper<SecBalanceSheetsVo>().in("accession_number", secFinancialReportsVos.stream().map(SecFinancialReportsVo::getAccessionNumber).collect(Collectors.toList())));
            secBalanceSheetsVoService.saveBatch(secBalanceSheetsVos);
            log.info("成功插入 {} 条资产负债表数据", secBalanceSheetsVos.size());
        }
        if (!secCashFlowStatementsVos.isEmpty()) {
            secCashFlowStatementsVoService.remove(new QueryWrapper<SecCashFlowStatementsVo>().in("accession_number", secFinancialReportsVos.stream().map(SecFinancialReportsVo::getAccessionNumber).collect(Collectors.toList())));
            secCashFlowStatementsVoService.saveBatch(secCashFlowStatementsVos);
            log.info("成功插入 {} 条现金流量表数据", secCashFlowStatementsVos.size());
        }
        if (!secEquityAndShareholderReturnVos.isEmpty()) {
            secEquityAndShareholderReturnVoService.remove(new QueryWrapper<SecEquityAndShareholderReturnVo>().in("accession_number", secFinancialReportsVos.stream().map(SecFinancialReportsVo::getAccessionNumber).collect(Collectors.toList())));
            secEquityAndShareholderReturnVoService.saveBatch(secEquityAndShareholderReturnVos);
            log.info("成功插入 {} 条股权与股东回报数据", secEquityAndShareholderReturnVos.size());
        }
    }

    /**
     * 处理利润表指标数据填充
     * @param usGaapNode JSON节点
     * @param secFinancialReportsVos 财务报告列表
     * @return 填充好的利润表数据列表
     */
    private List<SecIncomeStatementsVo> processIncomeStatementsMetrics(JsonNode usGaapNode, List<SecFinancialReportsVo> secFinancialReportsVos) {
        HashMap<String, SecIncomeStatementsVo> incomeStatementsMap = new HashMap<>();
        // 遍历所有财务报告
        for (SecFinancialReportsVo report : secFinancialReportsVos) {
            String accessionNumber = report.getAccessionNumber();
            incomeStatementsMap.computeIfAbsent(
                    accessionNumber,
                    k -> {
                        SecIncomeStatementsVo vo = new SecIncomeStatementsVo();
                        vo.setReportId(report.getId());
                        vo.setAccessionNumber(accessionNumber);
                        return vo;
                    }
            );
        }

        // 遍历所有利润表指标枚举
        for (SecIncomeStatementsMetricEnum metric : SecIncomeStatementsMetricEnum.values()) {
            String metricName = metric.getMetric();
            JsonNode metricNode = usGaapNode.path(metricName);
            if (!metricNode.isMissingNode()) {
                JsonNode unitsNode = metricNode.path("units").path("USD");
                if (unitsNode.isArray()) {
                    unitsNode.forEach(item -> {
                        String accessionNumber = item.get("accn").asText();
                        SecIncomeStatementsVo incomeStatement = incomeStatementsMap.get(accessionNumber);
                        if (incomeStatement != null && item.has("val")) {
                            BigDecimal value = item.get("val").decimalValue();
                            setMetricValue(incomeStatement, metric, value);
                            incomeStatement.setFiscalYear(item.get("fy") != null ? item.get("fy").asText() : null);
                            incomeStatement.setFiscalPeriod(item.get("fp") != null ? item.get("fp").asText() : null);
                        }
                    });
                }
            }
        }
        // 过滤掉除了id和reportId外其他字段都为空的对象
        return incomeStatementsMap.values().stream()
                .filter(this::hasValidIncomeStatementsData)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    private List<SecBalanceSheetsVo> processBalanceSheetsMetrics(JsonNode usGaapNode, List<SecFinancialReportsVo> secFinancialReportsVos) {
        HashMap<String, SecBalanceSheetsVo> balanceSheetsMap = new HashMap<>();
        for (SecFinancialReportsVo report : secFinancialReportsVos) {
            String accessionNumber = report.getAccessionNumber();
            balanceSheetsMap.computeIfAbsent(
                    accessionNumber,
                    k -> {
                        SecBalanceSheetsVo vo = new SecBalanceSheetsVo();
                        vo.setReportId(report.getId());
                        vo.setAccessionNumber(accessionNumber);
                        return vo;
                    }
            );
        }
        for (SecBalanceSheetsMetricEnum metric : SecBalanceSheetsMetricEnum.values()) {
            String metricName = metric.getMetric();
            JsonNode metricNode = usGaapNode.path(metricName);
            if (!metricNode.isMissingNode()) {
                JsonNode unitsNode = metricNode.path("units").path("USD");
                if (unitsNode.isArray()) {
                    unitsNode.forEach(item -> {
                        String accessionNumber = item.get("accn").asText();
                        SecBalanceSheetsVo balanceSheet = balanceSheetsMap.get(accessionNumber);
                        if (balanceSheet != null && item.has("val")) {
                            BigDecimal value = item.get("val").decimalValue();
                            setMetricValue(balanceSheet, metric, value);
                            balanceSheet.setFiscalYear(item.get("fy") != null ? item.get("fy").asText() : null);
                            balanceSheet.setFiscalPeriod(item.get("fp") != null ? item.get("fp").asText() : null);
                        }
                    });
                }
            }
        }
        // 过滤掉除了id和reportId外其他字段都为空的对象
        return balanceSheetsMap.values().stream()
                .filter(this::hasValidBalanceSheetsData)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    private List<SecCashFlowStatementsVo> processCashFlowStatementsMetrics(JsonNode usGaapNode, List<SecFinancialReportsVo> secFinancialReportsVos) {
        HashMap<String, SecCashFlowStatementsVo> cashFlowStatementsMap = new HashMap<>();
        for (SecFinancialReportsVo report : secFinancialReportsVos) {
            String accessionNumber = report.getAccessionNumber();
            cashFlowStatementsMap.computeIfAbsent(
                    accessionNumber,
                    k -> {
                        SecCashFlowStatementsVo vo = new SecCashFlowStatementsVo();
                        vo.setReportId(report.getId());
                        vo.setAccessionNumber(accessionNumber);
                        return vo;
                    }
            );
        }
        for (SecCashFolwStatementsMetricEnum metric : SecCashFolwStatementsMetricEnum.values()) {
            String metricName = metric.getMetric();
            JsonNode metricNode = usGaapNode.path(metricName);
            if (!metricNode.isMissingNode()) {
                JsonNode unitsNode = metricNode.path("units").path("USD");
                if (unitsNode.isArray()) {
                    unitsNode.forEach(item -> {
                        String accessionNumber = item.get("accn").asText();
                        SecCashFlowStatementsVo cashFlowStatement = cashFlowStatementsMap.get(accessionNumber);
                        if (cashFlowStatement != null && item.has("val")) {
                            BigDecimal value = item.get("val").decimalValue();
                            setMetricValue(cashFlowStatement, metric, value);
                            cashFlowStatement.setFiscalYear(item.get("fy") != null ?item.get("fy").asText() : null);
                            cashFlowStatement.setFiscalPeriod(item.get("fp") != null ? item.get("fp").asText() : null);
                        }
                    });
                }
            }
        }
        // 过滤掉除了id和reportId外其他字段都为空的对象
        return cashFlowStatementsMap.values().stream()
                .filter(this::hasValidCashFlowStatementsData)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    private List<SecEquityAndShareholderReturnVo> processEquityAndShareholderReturnMetrics(JsonNode usGaapNode, List<SecFinancialReportsVo> secFinancialReportsVos) {
        HashMap<String, SecEquityAndShareholderReturnVo> equityAndShareholderReturnMap = new HashMap<>();
        for (SecFinancialReportsVo report : secFinancialReportsVos) {
            String accessionNumber = report.getAccessionNumber();
            equityAndShareholderReturnMap.computeIfAbsent(
                    accessionNumber,
                    k -> {
                        SecEquityAndShareholderReturnVo vo = new SecEquityAndShareholderReturnVo();
                        vo.setReportId(report.getId());
                        vo.setAccessionNumber(accessionNumber);
                        return vo;
                    }
            );
        }
        for (SecEquityAndShareholderReturnMetricEnum metric : SecEquityAndShareholderReturnMetricEnum.values()) {
            String metricName = metric.getMetric();
            JsonNode metricNode = usGaapNode.path(metricName);
            if (!metricNode.isMissingNode()) {
                JsonNode unitsNode = metricNode.path("units").path("USD");
                if (unitsNode.isArray()) {
                    unitsNode.forEach(item -> {
                        String accessionNumber = item.get("accn").asText();
                        SecEquityAndShareholderReturnVo equityAndShareholderReturn = equityAndShareholderReturnMap.get(accessionNumber);
                        if (equityAndShareholderReturn != null && item.has("val")) {
                            BigDecimal value = item.get("val").decimalValue();
                            setMetricValue(equityAndShareholderReturn, metric, value);
                            equityAndShareholderReturn.setFiscalYear(item.get("fy") != null ? item.get("fy").asText() : null);
                            equityAndShareholderReturn.setFiscalPeriod(item.get("fp") != null ? item.get("fp").asText() : null);
                        }
                    });
                }
            }
        }
        // 过滤掉除了id和reportId外其他字段都为空的对象
        return equityAndShareholderReturnMap.values().stream()
                .filter(this::hasValidEquityAndShareholderReturnData)
                .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }

    /**
     * 根据指标枚举设置对应的字段值
     * @param incomeStatement 利润表对象
     * @param metric 指标枚举
     * @param value 值
     */
    private void setMetricValue(SecIncomeStatementsVo incomeStatement, SecIncomeStatementsMetricEnum metric, BigDecimal value) {
        switch (metric) {
            case REVENUES:
                incomeStatement.setRevenues(value);
                break;
            case COST_OF_REVENUE:
                incomeStatement.setCostOfRevenue(value);
                break;
            case GROSS_PROFIT:
                incomeStatement.setGrossProfit(value);
                break;
            case RESEARCH_AND_DEVELOPMENT_EXPENSE:
                incomeStatement.setResearchAndDevelopmentExpense(value);
                break;
            case SELLING_GENERAL_AND_ADMINISTRATIVE_EXPENSE:
                incomeStatement.setSellingGeneralAndAdministrativeExpense(value);
                break;
            case OPERATING_INCOME_LOSS:
                incomeStatement.setOperatingIncomeLoss(value);
                break;
            case NONOPERATING_INCOME_EXPENSE:
                incomeStatement.setNonoperatingIncomeExpense(value);
                break;
            case INTEREST_EXPENSE:
                incomeStatement.setInterestExpense(value);
                break;
            case INVESTMENT_INCOME_INTEREST:
                incomeStatement.setInvestmentIncomeInterest(value);
                break;
            case INCOME_BEFORE_TAX:
                incomeStatement.setIncomeBeforeTax(value);
                break;
            case INCOME_TAX_EXPENSE_BENEFIT:
                incomeStatement.setIncomeTaxExpenseBenefit(value);
                break;
            case NET_INCOME_LOSS:
                incomeStatement.setNetIncomeLoss(value);
                break;
            case EARNINGS_PER_SHARE_BASIC:
                incomeStatement.setEarningsPerShareBasic(value);
                break;
            case EARNINGS_PER_SHARE_DILUTED:
                incomeStatement.setEarningsPerShareDiluted(value);
                break;
            case OPERATING_EXPENSES:
                incomeStatement.setOperatingExpenses(value);
                break;
            case DEPRECIATION_AND_AMORTIZATION:
                incomeStatement.setDepreciationAndAmortization(value);
                break;
            case SHARE_BASED_COMPENSATION:
                incomeStatement.setShareBasedCompensation(value);
                break;
            case GAINS_LOSSES_ON_EXTINGUISHMENT_OF_DEBT:
                incomeStatement.setGainsLossesOnExtinguishmentOfDebt(value);
                break;
            case GAIN_LOSS_ON_INVESTMENTS:
                incomeStatement.setGainLossOnInvestments(value);
                break;
            case COMPREHENSIVE_INCOME_NET_OF_TAX:
                incomeStatement.setComprehensiveIncomeNetOfTax(value);
                break;
            default:
                log.warn("未处理的利润表指标: {}", metric.getMetric());
                break;
        }
    }

    private void setMetricValue(SecBalanceSheetsVo secBalanceSheetsVo, SecBalanceSheetsMetricEnum metric, BigDecimal value) {
        switch (metric) {
            case ASSETS:
                secBalanceSheetsVo.setAssets(value);
                break;
            case ASSETS_CURRENT:
                secBalanceSheetsVo.setAssetsCurrent(value);
                break;
            case CASH_AND_CASH_EQUIVALENTS:
                secBalanceSheetsVo.setCashAndCashEquivalents(value);
                break;
            case MARKETABLE_SECURITIES_CURRENT:
                secBalanceSheetsVo.setMarketableSecuritiesCurrent(value);
                break;
            case ACCOUNTS_RECEIVABLE_NET_CURRENT:
                secBalanceSheetsVo.setAccountsReceivableNetCurrent(value);
                break;
            case INVENTORY_NET:
                secBalanceSheetsVo.setInventoryNet(value);
                break;
            case NONCURRENT_ASSETS:
                secBalanceSheetsVo.setNoncurrentAssets(value);
                break;
            case PROPERTY_PLANT_AND_EQUIPMENT_NET:
                secBalanceSheetsVo.setPropertyPlantAndEquipmentNet(value);
                break;
            case GOODWILL:
                secBalanceSheetsVo.setGoodwill(value);
                break;
            case INTANGIBLE_ASSETS_NET_EXCLUDING_GOODWILL:
                secBalanceSheetsVo.setIntangibleAssetsNetExcludingGoodwill(value);
                break;
            case LIABILITIES:
                secBalanceSheetsVo.setLiabilities(value);
                break;
            case LIABILITIES_CURRENT:
                secBalanceSheetsVo.setLiabilitiesCurrent(value);
                break;
            case ACCOUNTS_PAYABLE_CURRENT:
                secBalanceSheetsVo.setAccountsPayableCurrent(value);
                break;
            case ACCRUED_LIABILITIES_CURRENT:
                secBalanceSheetsVo.setAccruedLiabilitiesCurrent(value);
                break;
            case DEBT_CURRENT:
                secBalanceSheetsVo.setDebtCurrent(value);
                break;
            case DEFERRED_REVENUE_CURRENT:
                secBalanceSheetsVo.setDeferredRevenueCurrent(value);
                break;
            case LIABILITIES_NONCURRENT:
                secBalanceSheetsVo.setLiabilitiesNoncurrent(value);
                break;
            case LONG_TERM_DEBT_NONCURRENT:
                secBalanceSheetsVo.setLongTermDebtNoncurrent(value);
                break;
            case OPERATING_LEASE_LIABILITY_NONCURRENT:
                secBalanceSheetsVo.setOperatingLeaseLiabilityNoncurrent(value);
                break;
            case STOCKHOLDERS_EQUITY:
                secBalanceSheetsVo.setStockholdersEquity(value);
                break;
            case RETAINED_EARNINGS_ACCUMULATED_DEFICIT:
                secBalanceSheetsVo.setRetainedEarningsAccumulatedDeficit(value);
                break;
            case ADDITIONAL_PAID_IN_CAPITAL:
                secBalanceSheetsVo.setAdditionalPaidInCapital(value);
                break;
            case TREASURY_STOCK_VALUE:
                secBalanceSheetsVo.setTreasuryStockValue(value);
                break;
            case DEFERRED_TAX_ASSETS_NET:
                secBalanceSheetsVo.setDeferredTaxAssetsNet(value);
                break;
            case DEFERRED_TAX_LIABILITIES_NET:
                secBalanceSheetsVo.setDeferredTaxLiabilitiesNet(value);
                break;
            case OPERATING_LEASE_RIGHT_OF_USE_ASSET:
                secBalanceSheetsVo.setOperatingLeaseRightOfUseAsset(value);
                break;
            case LIABILITIES_AND_STOCKHOLDERS_EQUITY:
                secBalanceSheetsVo.setLiabilitiesAndStockholdersEquity(value);
                break;
            default:
                log.warn("未处理的资产负债表指标: {}", metric.getMetric());
                break;
        }
    }

    private void setMetricValue(SecCashFlowStatementsVo secCashFlowStatementsVo, SecCashFolwStatementsMetricEnum metric, BigDecimal value) {
        switch (metric) {
            case NET_CASH_FROM_OPERATING_ACTIVITIES:
                secCashFlowStatementsVo.setNetCashFromOperatingActivities(value);
                break;
            case NET_CASH_FROM_INVESTING_ACTIVITIES:
                secCashFlowStatementsVo.setNetCashFromInvestingActivities(value);
                break;
            case PAYMENTS_FOR_PROPERTY_PLANT_AND_EQUIPMENT:
                secCashFlowStatementsVo.setPaymentsForPropertyPlantAndEquipment(value);
                break;
            case PAYMENTS_TO_ACQUIRE_BUSINESSES:
                secCashFlowStatementsVo.setPaymentsToAcquireBusinesses(value);
                break;
            case PROCEEDS_FROM_MARKETABLE_SECURITIES:
                secCashFlowStatementsVo.setProceedsFromMarketableSecurities(value);
                break;
            case NET_CASH_FROM_FINANCING_ACTIVITIES:
                secCashFlowStatementsVo.setNetCashFromFinancingActivities(value);
                break;
            case PROCEEDS_FROM_ISSUANCE_OF_DEBT:
                secCashFlowStatementsVo.setProceedsFromIssuanceOfDebt(value);
                break;
            case REPAYMENTS_OF_DEBT:
                secCashFlowStatementsVo.setRepaymentsOfDebt(value);
                break;
            case PAYMENTS_FOR_REPURCHASE_OF_COMMON_STOCK:
                secCashFlowStatementsVo.setPaymentsForRepurchaseOfCommonStock(value);
                break;
            case PAYMENTS_OF_DIVIDENDS:
                secCashFlowStatementsVo.setPaymentsOfDividends(value);
                break;
            case PROCEEDS_FROM_ISSUANCE_OF_COMMON_STOCK:
                secCashFlowStatementsVo.setProceedsFromIssuanceOfCommonStock(value);
                break;
            case CASH_PERIOD_INCREASE_DECREASE:
                secCashFlowStatementsVo.setCashPeriodIncreaseDecrease(value);
                break;
            case DEPRECIATION_DEPLETION_AND_AMORTIZATION_CASHFLOW:
                secCashFlowStatementsVo.setDepreciationDepletionAndAmortizationCashflow(value);
                break;
            case SHARE_BASED_COMPENSATION_CASHFLOW:
                secCashFlowStatementsVo.setShareBasedCompensationCashflow(value);
                break;
            case INCREASE_DECREASE_IN_ACCOUNTS_RECEIVABLE:
                secCashFlowStatementsVo.setIncreaseDecreaseInAccountsReceivable(value);
                break;
            case INCREASE_DECREASE_IN_INVENTORIES:
                secCashFlowStatementsVo.setIncreaseDecreaseInInventories(value);
                break;
            case INCREASE_DECREASE_IN_ACCOUNTS_PAYABLE:
                secCashFlowStatementsVo.setIncreaseDecreaseInAccountsPayable(value);
                break;
            case INCREASE_DECREASE_IN_CONTRACT_LIABILITY:
                secCashFlowStatementsVo.setIncreaseDecreaseInContractLiability(value);
                break;
            default:
                log.warn("未处理的现金流量表指标: {}", metric.getMetric());
                break;
        }
    }

    private void setMetricValue(SecEquityAndShareholderReturnVo secEquityAndShareholderReturnVo, SecEquityAndShareholderReturnMetricEnum metric, BigDecimal value) {
        switch (metric) {
            case COMMON_STOCK_SHARES_OUTSTANDING:
                secEquityAndShareholderReturnVo.setCommonStockSharesOutstanding(value.longValue());
                break;
            case WEIGHTED_AVERAGE_SHARES_BASIC:
                secEquityAndShareholderReturnVo.setWeightedAverageSharesBasic(value.longValue());
                break;
            case WEIGHTED_AVERAGE_SHARES_DILUTED:
                secEquityAndShareholderReturnVo.setWeightedAverageSharesDiluted(value.longValue());
                break;
            case REPURCHASE_AUTHORIZED_AMOUNT:
                secEquityAndShareholderReturnVo.setRepurchaseAuthorizedAmount(value);
                break;
            case REPURCHASE_REMAINING_AMOUNT:
                secEquityAndShareholderReturnVo.setRepurchaseRemainingAmount(value);
                break;
            case COMMON_STOCK_SHARES_AUTHORIZED:
                secEquityAndShareholderReturnVo.setCommonStockSharesAuthorized(value.longValue());
                break;
            default:
                log.warn("未处理的股权与股东回报指标: {}", metric.getMetric());
                break;
        }
    }

    /**
     * 检查利润表数据是否有有效数据（除了id和reportId外是否还有其他非空字段）
     */
    private boolean hasValidIncomeStatementsData(SecIncomeStatementsVo vo) {
        return vo.getRevenues() != null ||
                vo.getCostOfRevenue() != null ||
                vo.getGrossProfit() != null ||
                vo.getResearchAndDevelopmentExpense() != null ||
                vo.getSellingGeneralAndAdministrativeExpense() != null ||
                vo.getOperatingIncomeLoss() != null ||
                vo.getNonoperatingIncomeExpense() != null ||
                vo.getInterestExpense() != null ||
                vo.getInvestmentIncomeInterest() != null ||
                vo.getIncomeBeforeTax() != null ||
                vo.getIncomeTaxExpenseBenefit() != null ||
                vo.getNetIncomeLoss() != null ||
                vo.getEarningsPerShareBasic() != null ||
                vo.getEarningsPerShareDiluted() != null ||
                vo.getOperatingExpenses() != null ||
                vo.getDepreciationAndAmortization() != null ||
                vo.getShareBasedCompensation() != null ||
                vo.getGainsLossesOnExtinguishmentOfDebt() != null ||
                vo.getGainLossOnInvestments() != null ||
                vo.getComprehensiveIncomeNetOfTax() != null;
    }

    /**
     * 检查资产负债表数据是否有有效数据（除了id和reportId外是否还有其他非空字段）
     */
    private boolean hasValidBalanceSheetsData(SecBalanceSheetsVo vo) {
        return vo.getAssets() != null ||
                vo.getAssetsCurrent() != null ||
                vo.getCashAndCashEquivalents() != null ||
                vo.getMarketableSecuritiesCurrent() != null ||
                vo.getAccountsReceivableNetCurrent() != null ||
                vo.getInventoryNet() != null ||
                vo.getNoncurrentAssets() != null ||
                vo.getPropertyPlantAndEquipmentNet() != null ||
                vo.getGoodwill() != null ||
                vo.getIntangibleAssetsNetExcludingGoodwill() != null ||
                vo.getLiabilities() != null ||
                vo.getLiabilitiesCurrent() != null ||
                vo.getAccountsPayableCurrent() != null ||
                vo.getAccruedLiabilitiesCurrent() != null ||
                vo.getDebtCurrent() != null ||
                vo.getDeferredRevenueCurrent() != null ||
                vo.getLiabilitiesNoncurrent() != null ||
                vo.getLongTermDebtNoncurrent() != null ||
                vo.getOperatingLeaseLiabilityNoncurrent() != null ||
                vo.getStockholdersEquity() != null ||
                vo.getRetainedEarningsAccumulatedDeficit() != null ||
                vo.getAdditionalPaidInCapital() != null ||
                vo.getTreasuryStockValue() != null ||
                vo.getDeferredTaxAssetsNet() != null ||
                vo.getDeferredTaxLiabilitiesNet() != null ||
                vo.getOperatingLeaseRightOfUseAsset() != null ||
                vo.getLiabilitiesAndStockholdersEquity() != null;
    }

    /**
     * 检查现金流量表数据是否有有效数据（除了id和reportId外是否还有其他非空字段）
     */
    private boolean hasValidCashFlowStatementsData(SecCashFlowStatementsVo vo) {
        return vo.getNetCashFromOperatingActivities() != null ||
                vo.getNetCashFromInvestingActivities() != null ||
                vo.getPaymentsForPropertyPlantAndEquipment() != null ||
                vo.getPaymentsToAcquireBusinesses() != null ||
                vo.getProceedsFromMarketableSecurities() != null ||
                vo.getNetCashFromFinancingActivities() != null ||
                vo.getProceedsFromIssuanceOfDebt() != null ||
                vo.getRepaymentsOfDebt() != null ||
                vo.getPaymentsForRepurchaseOfCommonStock() != null ||
                vo.getPaymentsOfDividends() != null ||
                vo.getProceedsFromIssuanceOfCommonStock() != null ||
                vo.getCashPeriodIncreaseDecrease() != null ||
                vo.getDepreciationDepletionAndAmortizationCashflow() != null ||
                vo.getShareBasedCompensationCashflow() != null ||
                vo.getIncreaseDecreaseInAccountsReceivable() != null ||
                vo.getIncreaseDecreaseInInventories() != null ||
                vo.getIncreaseDecreaseInAccountsPayable() != null ||
                vo.getIncreaseDecreaseInContractLiability() != null;
    }

    /**
     * 检查股权与股东回报数据是否有有效数据（除了id和reportId外是否还有其他非空字段）
     */
    private boolean hasValidEquityAndShareholderReturnData(SecEquityAndShareholderReturnVo vo) {
        return vo.getCommonStockSharesOutstanding() != null ||
                vo.getWeightedAverageSharesBasic() != null ||
                vo.getWeightedAverageSharesDiluted() != null ||
                vo.getRepurchaseAuthorizedAmount() != null ||
                vo.getRepurchaseRemainingAmount() != null ||
                vo.getCommonStockSharesAuthorized() != null;
    }
}